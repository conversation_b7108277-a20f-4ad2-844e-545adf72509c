import { defineStore } from 'pinia';
import { db } from '../firebase/config';
import { doc, onSnapshot, updateDoc } from 'firebase/firestore';
import { useExtensionsStore } from './extensionsStore';

/**
 * @module store/meetingConfigStore
 * @description Manages the configuration of an active meeting, including its settings,
 * active core features, and enabled extensions. It synchronizes this data with Firebase
 * in real-time.
 */
// Define initialActiveFeatures for use in state and merging
const initialActiveFeatures = {
  recording: false,
  whiteboard: false,
  breakoutRooms: false,
  chat: true, // Typically enabled by default
  reactions: true,
  virtualBackground: false,
  audibleImpairedSystem: false,
  // Add other defined core features here
};

export const useMeetingConfigStore = defineStore('meetingConfig', {
  state: () => ({
    /** @type {String|null} The ID of the currently active meeting. */
    currentMeetingId: null,
    /** @type {Object} Basic settings for the meeting. */
    meetingSettings: {
      name: 'TheMeet Meeting',
      requireApproval: false,
      hostUserId: null,
    },
    /**
     * @type {Object<String, Boolean>} Tracks the state of core, built-in features.
     * Keys are feature names (e.g., 'whiteboard'), values are boolean (enabled/disabled).
     */
    activeFeatures: { ...initialActiveFeatures },

    /**
     * @type {Array<Object>} Array of active extension objects.
     * Each object typically includes the extension's manifest and any instance-specific configuration.
     */
    activeExtensions: [],
    /**
     * @type {String} Policy for who can share invite link: 'host' or 'all'.
     */
    linkSharingPolicy: 'host', // or 'all'
    /**
     * @type {Array<Object>} Tracks who shared/joined via link: [{ sharedBy, joinedBy, joinedAt }]
     */
    joinTracking: [],
    /** @type {Boolean} Indicates if meeting configuration is currently being loaded. */
    isLoading: false,
    /** @type {String|null} Stores any error message encountered during loading or updates. */
    error: null,
    /** @private @type {Function|null} Firebase listener unsubscribe function. */
    _firebaseUnsubscribe: null,
  }),

  getters: {
    /**
     * Checks if a specific core feature is active.
     * @param {Object} state The store's state.
     * @returns {Function} A function that takes a feature name and returns its active status.
     */
    isFeatureActive: (state) => (featureName) => {
      return !!state.activeFeatures[featureName];
    },
    getExtensionConfig: (state) => (extensionId) => {
      const extension = state.activeExtensions.find(ext => ext.id === extensionId);
      return extension ? extension.config : null;
    },
    getAllActiveExtensions: (state) => {
      return state.activeExtensions;
    },
    getMeetingName: (state) => {
      return state.meetingSettings.name;
    },
    isHost: (state) => (userId) => {
      return state.meetingSettings.hostUserId === userId;
    },
    /**
     * Returns the link sharing policy ('host' or 'all').
     */
    getLinkSharingPolicy: (state) => state.linkSharingPolicy,
    /**
     * Returns the join tracking array.
     */
    getJoinTracking: (state) => state.joinTracking,
  },

  actions: {
    /**
     * Initializes the store with settings for a new meeting, typically called before
     * the meeting is created in Firebase.
     * @param {Object} settings - Meeting settings like name, requireApproval.
     * @param {String} hostUserId - The ID of the user creating and hosting the meeting.
     */
    initializeNewMeeting(settings, hostUserId) {
      this.meetingSettings = { ...this.meetingSettings, ...settings, hostUserId };
      // Note: activeFeatures and activeExtensions are typically set by user interaction
      // in Home.vue and then passed to firebase/meetings.createMeeting.
      // This function primarily sets up meetingSettings and hostUserId in the store.
    },

    /**
     * Loads the configuration for a given meeting ID from Firebase and sets up
     * a real-time listener for updates.
     * @param {String} meetingId - The ID of the meeting to load.
     * @param {String} currentUserId - The ID of the current user (not directly used here but good for context).
     */
    async loadMeetingConfiguration(meetingId, currentUserId) {
      if (this._firebaseUnsubscribe) {
        this._firebaseUnsubscribe();
        this._firebaseUnsubscribe = null;
      }
      this.currentMeetingId = meetingId;
      this.isLoading = true;
      this.error = null;

      const meetingRef = doc(db, 'meetings', meetingId);

      this._firebaseUnsubscribe = onSnapshot(meetingRef, (docSnap) => {
        if (docSnap.exists()) {
          const data = docSnap.data();
          this.meetingSettings = {
            name: data.meetingName || 'TheMeet Meeting',
            requireApproval: data.requireApproval || false, // Ensure this matches Firebase field name
            hostUserId: data.createdBy,
          };
          // Merge fetched features with local defaults to ensure all keys exist
          // Use a constant for initial features to avoid referencing this.state (undefined in Pinia)
this.activeFeatures = { ...initialActiveFeatures, ...(data.features || {}) };


          const extensionsStore = useExtensionsStore();
          this.activeExtensions = (data.extensions || []).map(extConfig => {
            const manifest = extensionsStore.getExtensionById(extConfig.id);
            // Return hydrated extension if manifest found, otherwise could return basic info or null
            return manifest ? { ...manifest, config: extConfig.config || {} } : { id: extConfig.id, name: extConfig.name || 'Unknown Extension', config: extConfig.config || {}, manifestNotFound: true };
          });//.filter(ext => ext !== null); // Keep even if manifest not found to indicate it's active

          this.isLoading = false;
        } else {
          this.error = 'Meeting not found or you do not have access.';
          this.isLoading = false;
          console.error(`Meeting with ID ${meetingId} not found.`);
        }
      }, (err) => {
        this.error = `Error loading meeting: ${err.message}`;
        this.isLoading = false;
        console.error('Firebase snapshot error:', err);
      });
    },

    /**
     * @private
     * Helper function to update the meeting document in Firebase.
     * @param {Object} partialData - An object containing fields to update.
     */
    async _updateFirebaseMeeting(partialData) {
      if (!this.currentMeetingId) {
        console.error("No currentMeetingId set, cannot update Firebase.");
        this.error = "Cannot update: No active meeting.";
        return;
      }
      try {
        const meetingRef = doc(db, 'meetings', this.currentMeetingId);
        await updateDoc(meetingRef, partialData);
      } catch (err) {
        console.error("Error updating Firebase meeting:", err);
        this.error = `Failed to update meeting setting: ${err.message}`;
        // Consider implementing a rollback for optimistic updates here if necessary
      }
    },

    /**
     * Toggles the active state of a core feature and persists the change to Firebase.
     * @param {String} featureName - The key of the feature in `activeFeatures`.
     */
    async toggleFeature(featureName) {
      if (typeof this.activeFeatures[featureName] === 'undefined') {
        console.warn(`Feature ${featureName} is not defined in activeFeatures state.`);
        return;
      }
      const newState = !this.activeFeatures[featureName];
      this.activeFeatures[featureName] = newState; // Optimistic local update

      await this._updateFirebaseMeeting({ [`features.${featureName}`]: newState });
    },

    /**
     * Adds an extension to the current meeting and persists to Firebase.
     * Assumes extension manifest is available from `extensionsStore`.
     * @param {String} extensionId - The ID of the extension to add.
     */
    async addExtensionToMeeting(extensionId) {
      const extensionsStore = useExtensionsStore();
      const extensionManifest = extensionsStore.getExtensionById(extensionId);

      if (!extensionManifest) {
        this.error = `Extension manifest for ${extensionId} not found.`;
        console.error(this.error);
        return;
      }

      if (this.activeExtensions.some(ext => ext.id === extensionId)) {
        console.warn(`Extension ${extensionId} is already active.`);
        return;
      }

      // Prepare the extension instance for the activeExtensions array (includes manifest)
      const newExtensionForState = {
        ...extensionManifest, // Spread the full manifest
        config: extensionManifest.defaultConfig || {} // Apply default config
      };
      this.activeExtensions.push(newExtensionForState); // Optimistic update

      // Prepare a more minimal representation for Firebase storage
      const firebaseExtensionsArray = this.activeExtensions.map(ext => ({
        id: ext.id,
        name: ext.name, // Store name for convenience
        config: ext.config || {}
      }));
      await this._updateFirebaseMeeting({ extensions: firebaseExtensionsArray });
    },

    /**
     * Removes an extension from the current meeting and persists to Firebase.
     * @param {String} extensionId - The ID of the extension to remove.
     */
    async removeExtensionFromMeeting(extensionId) {
      this.activeExtensions = this.activeExtensions.filter(ext => ext.id !== extensionId); // Optimistic update

      const firebaseExtensionsArray = this.activeExtensions.map(ext => ({
        id: ext.id,
        name: ext.name,
        config: ext.config || {}
      }));
      await this._updateFirebaseMeeting({ extensions: firebaseExtensionsArray });
    },

    /**
     * Updates the instance-specific configuration for an active extension.
     * @param {String} extensionId - The ID of the extension whose config is to be updated.
     * @param {Object} newConfig - The new configuration object to merge with existing.
     */
    async updateExtensionMeetingConfig(extensionId, newConfig) {
      const extIndex = this.activeExtensions.findIndex(ext => ext.id === extensionId);
      if (extIndex === -1) {
        this.error = `Cannot update config: Extension ${extensionId} not active.`;
        console.error(this.error);
        return;
      }
      // Merge newConfig with existing config
      this.activeExtensions[extIndex].config = { ...this.activeExtensions[extIndex].config, ...newConfig }; // Optimistic

      const firebaseExtensionsArray = this.activeExtensions.map(ext => ({
        id: ext.id,
        name: ext.name,
        config: ext.config || {}
      }));
      await this._updateFirebaseMeeting({ extensions: firebaseExtensionsArray });
    },

    /**
     * Sets the host user ID for the meeting.
     * @param {String} hostUserId - The user ID of the host.
     */
    setMeetingHost(hostUserId) {
        this.meetingSettings.hostUserId = hostUserId;
        // This is usually set at meeting creation and loaded.
        // If it needs to be updated dynamically and persisted, _updateFirebaseMeeting would be used.
    },

    /**
     * Resets the store to its initial state and unsubscribes from Firebase listeners.
     * Typically called when a user leaves a meeting.
     */
    /**
     * Updates the link sharing policy (host/all) and persists to Firebase.
     * @param {String} policy - 'host' or 'all'
     */
    async updateLinkSharingPolicy(policy) {
      if (policy !== 'host' && policy !== 'all') return;
      this.linkSharingPolicy = policy;
      await this._updateFirebaseMeeting({ linkSharingPolicy: policy });
    },
    /**
     * Logs a join event for tracking who shared and who joined via link.
     * @param {String} sharedBy - userId of who shared the link
     * @param {String} joinedBy - userId of who joined via link
     * @param {String} joinedName - userName of who joined
     */
    async logJoinEvent(sharedBy, joinedBy, joinedName) {
      const event = { sharedBy, joinedBy, joinedName, joinedAt: Date.now() };
      this.joinTracking.push(event);
      await this._updateFirebaseMeeting({ joinTracking: this.joinTracking });
    },
    resetMeetingConfig() {
      if (this._firebaseUnsubscribe) {
        this._firebaseUnsubscribe();
        this._firebaseUnsubscribe = null;
      }
      this.currentMeetingId = null;
      this.meetingSettings = { name: 'TheMeet Meeting', requireApproval: false, hostUserId: null };
      // Reset to initial default activeFeatures state
      this.activeFeatures = {
        recording: false, whiteboard: false, breakoutRooms: false, chat: true, reactions: true,
        virtualBackground: false, audibleImpairedSystem: false
      };
      this.activeExtensions = [];
      this.isLoading = false;
      this.error = null;
    }
  }
});
